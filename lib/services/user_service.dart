import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart'
    hide User; // 确保 User 被隐藏
import '../models/user.dart'; // User 和 Message 都在这个文件里
import '../main.dart';
import 'dart:async';

/// 用户服务类，用于管理用户数据
class UserService {
  static const String _currentUserKey = 'current_user';
  static const String _usersStorageKey = 'users';
  static const String _messagesStorageKey = 'messages'; // 恢复
  
  // 使用ValueNotifier替代直接存储用户对象
  final ValueNotifier<User?> _currentUserNotifier = ValueNotifier(null);
  ValueListenable<User?> get currentUserNotifier => _currentUserNotifier;
  
  List<User> _users = [];
  List<Message> _messages = []; // 恢复
  
  // 认证状态变化监听器
  StreamSubscription<AuthState>? _authSubscription;

  /// 获取当前登录用户
  User? get currentUser => _currentUserNotifier.value;

  /// 检查用户是否已登录
  bool isLoggedIn() => _currentUserNotifier.value != null && supabase.auth.currentUser != null;

  /// 构造函数
  UserService() {
    // 在构造函数中初始化认证监听
    _setupAuthListener();
  }

  /// 设置认证状态监听
  void _setupAuthListener() {
    _authSubscription = supabase.auth.onAuthStateChange.listen((data) async {
      debugPrint('认证状态变化: ${data.event}');
      
      switch (data.event) {
        case AuthChangeEvent.signedIn:
          if (data.session?.user != null) {
            debugPrint('用户登录: ${data.session!.user.id}');
            await fetchCurrentUser();
          }
          break;
        case AuthChangeEvent.signedOut:
          debugPrint('用户登出');
          await clearCurrentUser();
          break;
        case AuthChangeEvent.userUpdated:
          debugPrint('用户信息更新');
          if (data.session?.user != null) {
            await fetchCurrentUser();
          }
          break;
        case AuthChangeEvent.tokenRefreshed:
          debugPrint('令牌刷新');
          if (_currentUserNotifier.value == null && data.session?.user != null) {
            await fetchCurrentUser();
          }
          break;
        default:
          break;
      }
    });
  }

  /// 初始化用户服务
  Future<void> initialize() async {
    debugPrint('初始化用户服务');
    final authUser = supabase.auth.currentUser;
    debugPrint('初始化时的身份验证状态: ${authUser != null ? "已登录" : "未登录"}');
    
    await _loadCurrentUser();
    debugPrint('加载本地用户后状态: ${_currentUserNotifier.value != null ? "存在" : "不存在"}');

    // 如果本地有用户信息但Supabase没有登录，则尝试刷新会话
    if (_currentUserNotifier.value != null && supabase.auth.currentUser == null) {
      debugPrint('本地用户存在但Supabase会话不存在，尝试刷新会话');
      try {
        final session = await supabase.auth.refreshSession();
        if (session.user == null) {
          debugPrint('会话刷新失败，清除本地用户');
          // 会话刷新失败，清除本地用户信息
          await clearCurrentUser();
        } else {
          debugPrint('会话刷新成功');
        }
      } catch (e) {
        debugPrint('刷新会话失败: $e');
        await clearCurrentUser();
      }
    }

    // 如果Supabase已登录但本地没有用户信息，则获取用户信息
    if (_currentUserNotifier.value == null && supabase.auth.currentUser != null) {
      debugPrint('Supabase已登录但本地没有用户数据，尝试获取');
      try {
        await fetchCurrentUser();
        debugPrint('成功从Supabase获取用户: ${_currentUserNotifier.value?.username}');
      } catch (e) {
        debugPrint('获取用户信息失败，尝试创建新用户: $e');
        
        // 如果用户在身份验证中存在但在数据库中不存在，尝试创建用户
        final authUser = supabase.auth.currentUser!;
        final newUser = User(
          id: authUser.id,
          username: 'user_${authUser.id.substring(0, 8)}',
          nickname: 'user_${authUser.id.substring(0, 8)}',
          email: authUser.email ?? '',
        );
        
        try {
          // 尝试创建用户记录
          await createUserInDatabase(newUser);
          await setCurrentUser(newUser);
          debugPrint('成功创建并设置新用户');
        } catch (createError) {
          debugPrint('创建用户失败: $createError');
          // 不创建用户以继续应用运行
        }
      }
    }
    
    // 输出最终状态
    final finalAuthUser = supabase.auth.currentUser;
    debugPrint('初始化完成后状态:');
    debugPrint('- Supabase登录状态: ${finalAuthUser != null ? "已登录" : "未登录"}');
    debugPrint('- 本地用户状态: ${_currentUserNotifier.value != null ? "存在" : "不存在"}');
  }

  /// 使用邮箱和密码登录
  /// 如果用户不存在，则自动注册
  Future<User?> loginWithEmail(String email, String password) async {
    try {
      // 先尝试登录
      try {
        final response = await supabase.auth.signInWithPassword(
          email: email,
          password: password,
        );
        
        if (response.user != null) {
          // 登录成功，尝试获取用户信息
          try {
            final user = await fetchCurrentUser();
            return user;
          } catch (e) {
            debugPrint('获取用户信息失败，创建新用户记录: $e');
            // 如果用户在auth存在但在users表中不存在，创建新记录
            final newUser = User(
              id: response.user!.id,
              username: 'user_${response.user!.id.substring(0, 8)}',
              nickname: 'user_${response.user!.id.substring(0, 8)}',
              email: email,
            );
            
            // 保存到数据库
            await createUserInDatabase(newUser);
            await setCurrentUser(newUser);
            return newUser;
          }
        }
      } catch (e) {
        // 登录失败，可能是用户不存在，尝试注册
        debugPrint('登录失败，尝试注册: $e');
      }

      // 尝试注册
      final response = await supabase.auth.signUp(
        email: email,
        password: password,
        emailRedirectTo: null, // 不需要邮箱验证
      );

      if (response.user != null) {
        // 创建新用户
        final username = 'user_${response.user!.id.substring(0, 8)}';
        final newUser = User(
          id: response.user!.id,
          username: username,
          nickname: username,
          email: email,
        );

        // 保存到数据库
        try {
          await createUserInDatabase(newUser);
          debugPrint('用户成功创建在数据库中');
        } catch (e) {
          debugPrint('创建用户数据库记录失败: $e');
          // 如果创建失败，尝试退出并清除auth用户
          await supabase.auth.signOut();
          throw Exception('创建用户记录失败，请重试');
        }
        
        // 设置为当前用户
        await setCurrentUser(newUser);
        return newUser;
      }

      return null;
    } catch (e) {
      debugPrint('邮箱登录/注册失败: $e');
      return null;
    }
  }

  /// 从 Supabase 获取当前用户信息
  Future<User?> fetchCurrentUser() async {
    try {
      final authUser = supabase.auth.currentUser;
      if (authUser == null) {
        debugPrint('没有登录的用户');
        return null;
      }

      debugPrint('尝试获取用户信息: ${authUser.id}');
      final response =
          await supabase.from('users').select().eq('id', authUser.id).single();
      
      final user = User.fromJson(response);
      _currentUserNotifier.value = user;
      await _saveCurrentUser();
      debugPrint('成功获取用户信息: ${user.username}');
      return user;
    } catch (e) {
      debugPrint('获取用户信息失败: $e');
      rethrow; // 异常往上抛出，让调用者处理
    }
  }

  /// 在数据库中创建用户
  Future<void> createUserInDatabase(User user) async {
    try {
      debugPrint('尝试创建用户记录: ${user.id}, ${user.username}');
      
      // 首先检查该用户是否已存在
      final exists = await supabase
          .from('users')
          .select('id')
          .eq('id', user.id)
          .maybeSingle();
          
      if (exists != null) {
        debugPrint('用户记录已存在，不需要创建');
        return; // 用户已存在，无需创建
      }
      
      // 检查用户名是否已存在
      final usernameExists = await supabase
          .from('users')
          .select('id')
          .eq('username', user.username)
          .maybeSingle();
          
      String username = user.username;
      String nickname = user.nickname;
      
      if (usernameExists != null) {
        // 如果用户名已存在，添加随机字符
        nickname = '${user.nickname}_${DateTime.now().millisecondsSinceEpoch % 10000}';
        username = '${user.username}_${DateTime.now().millisecondsSinceEpoch % 10000}';
        debugPrint('用户名已存在，使用新用户名: $username');
      }
      
      // 创建用户记录
      final response = await supabase.from('users').insert({
        'id': user.id,
        'username': username,
        'nickname': nickname,
        'email': user.email,
        'phone': user.phoneNumber,
        'bio': user.bio,
        'avatar_url': user.avatarUrl,
        'published_spots': user.publishedSpots,
        'favorite_spots': user.favoriteSpots,
        'following': user.following,
        'followers': user.followers,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      }).select();
      
      debugPrint('用户创建成功: $response');
    } catch (e) {
      debugPrint('创建用户失败: $e');
      throw Exception('创建用户失败: $e');
    }
  }

  /// 设置当前用户
  Future<void> setCurrentUser(User user) async {
    debugPrint('设置当前用户: ${user.username}, ID: ${user.id}');
    _currentUserNotifier.value = user;
    await _saveCurrentUser();
  }

  /// 清除当前用户
  Future<void> clearCurrentUser() async {
    debugPrint('清除当前用户');
    
    // 清除内存中的用户对象
    final oldUserId = _currentUserNotifier.value?.id;
    _currentUserNotifier.value = null;
    
    try {
      // 清除SharedPreferences中的用户数据
      final prefs = await SharedPreferences.getInstance();
      final hadUser = await prefs.remove(_currentUserKey);
      debugPrint('本地用户数据移除${hadUser ? '成功' : '（数据不存在）'}');
      
      // 验证数据是否已经被删除
      if (prefs.getString(_currentUserKey) == null) {
        debugPrint('验证: 本地存储中的用户数据已完全清除');
      } else {
        debugPrint('警告: 本地存储中仍然存在用户数据');
      }
    } catch (e) {
      debugPrint('清除本地用户数据时出错: $e');
    }
    
    debugPrint('用户清除完成${oldUserId != null ? ' (ID: $oldUserId)' : ''}');
  }

  /// 根据ID获取用户
  Future<User?> getUserById(String id) async {
    try {
      final response =
          await supabase.from('users').select().eq('id', id).single();
      return User.fromJson(response);
    } catch (e) {
      debugPrint('获取用户失败: $e');
      return null;
    }
  }

  /// 根据用户名获取用户
  Future<User?> getUserByUsername(String username) async {
    try {
      final response =
          await supabase
              .from('users')
              .select()
              .eq('username', username)
              .single();
      return User.fromJson(response);
    } catch (e) {
      debugPrint('获取用户失败: $e');
      return null;
    }
  }

  // 用户数据缓存时间戳
  DateTime _lastUsersLoadTime = DateTime.now().subtract(
    const Duration(hours: 1),
  );
  static const int _usersCacheDuration = 5 * 60 * 1000; // 5分钟

  /// 从 Supabase 获取所有用户
  Future<List<User>> getAllUsers() async {
    // 检查缓存是否有效
    final now = DateTime.now();
    if (_users.isNotEmpty &&
        now.difference(_lastUsersLoadTime).inMilliseconds <
            _usersCacheDuration) {
      debugPrint('使用缓存的用户数据');
      return _users;
    }

    try {
      final response = await supabase.from('users').select();

      // 检查响应是否为空列表
      if ((response as List).isEmpty) {
        debugPrint('API返回空用户列表');

        // 如果有缓存，返回缓存
        if (_users.isNotEmpty) {
          return _users;
        }

        // 尝试从本地加载
        await _loadUsers();
        return _users;
      }

      // 处理正常响应
      final users = response.map((data) => User.fromJson(data)).toList();
      _users = users; // 更新本地缓存
      _lastUsersLoadTime = now; // 更新缓存时间戳

      // 异步保存到本地存储，不阻塞主流程
      _saveUsers().catchError((e) => debugPrint('保存用户数据失败: $e'));
      return users;
    } catch (e) {
      debugPrint('获取所有用户失败: $e');

      // 如果API调用失败但有缓存，返回缓存
      if (_users.isNotEmpty) {
        return _users;
      }

      // 尝试从本地加载
      await _loadUsers();
      return _users;
    }
  }

  /// 添加新用户
  Future<User> addUser(User user) async {
    _users.add(user);
    await _saveUsers();
    return user;
  }

  /// 更新用户信息
  Future<void> updateUser(User user) async {
    try {
      // 更新本地缓存
      final index = _users.indexWhere((u) => u.id == user.id);
      if (index != -1) {
        _users[index] = user;
        await _saveUsers();
      }

      // 如果是当前用户，更新当前用户缓存
      if (_currentUserNotifier.value?.id == user.id) {
        _currentUserNotifier.value = user;
        await _saveCurrentUser();
      }

      // 更新 Supabase 数据库
      await supabase
          .from('users')
          .update({
            'username': user.username,
            'nickname': user.nickname,
            'bio': user.bio,
            'phone': user.phoneNumber,
            'avatar_url': user.avatarUrl,
            'published_spots': user.publishedSpots,
            'favorite_spots': user.favoriteSpots,
            'following': user.following,
            'followers': user.followers,
          })
          .eq('id', user.id);
    } catch (e) {
      debugPrint('更新用户信息失败: $e');
      throw Exception('更新用户信息失败: $e');
    }
  }

  /// 删除用户
  Future<void> deleteUser(String id) async {
    _users.removeWhere((user) => user.id == id);
    await _saveUsers();
  }

  /// 关注用户
  Future<void> followUser(String userId, String targetUserId) async {
    final user = await getUserById(userId);
    final targetUser = await getUserById(targetUserId);

    if (user != null && targetUser != null) {
      if (!user.following.contains(targetUserId)) {
        user.following.add(targetUserId);
      }

      if (!targetUser.followers.contains(userId)) {
        targetUser.followers.add(userId);
      }

      await updateUser(user);
      await updateUser(targetUser);
    }
  }

  /// 取消关注用户
  Future<void> unfollowUser(String userId, String targetUserId) async {
    final user = await getUserById(userId);
    final targetUser = await getUserById(targetUserId);

    if (user != null && targetUser != null) {
      user.following.remove(targetUserId);
      targetUser.followers.remove(userId);
      await updateUser(user);
      await updateUser(targetUser);
    }
  }

  /// 添加发布的钓点
  Future<void> addPublishedSpot(String userId, String spotId) async {
    try {
      final user = await getUserById(userId);
      if (user != null) {
        if (!user.publishedSpots.contains(spotId)) {
          user.publishedSpots.add(spotId);
          await updateUser(user);
        }
      }
    } catch (e) {
      debugPrint('添加发布钓点失败: $e');
      throw Exception('添加发布钓点失败: $e');
    }
  }

  /// 添加收藏的钓点
  Future<void> addFavoriteSpot(String userId, String spotId) async {
    try {
      final user = await getUserById(userId);
      if (user != null) {
        if (!user.favoriteSpots.contains(spotId)) {
          user.favoriteSpots.add(spotId);
          await updateUser(user);
        }
      }
    } catch (e) {
      debugPrint('添加收藏钓点失败: $e');
      throw Exception('添加收藏钓点失败: $e');
    }
  }

  /// 移除收藏的钓点
  Future<void> removeFavoriteSpot(String userId, String spotId) async {
    try {
      final user = await getUserById(userId);
      if (user != null) {
        user.favoriteSpots.remove(spotId);
        await updateUser(user);
      }
    } catch (e) {
      debugPrint('移除收藏钓点失败: $e');
      throw Exception('移除收藏钓点失败: $e');
    }
  }

  /// 获取所有消息
  Future<List<Message>> getAllMessages() async {
    if (_messages.isEmpty) {
      await _loadMessages();
    }
    return _messages;
  }

  /// 获取用户的消息
  Future<List<Message>> getUserMessages(String userId) async {
    await getAllMessages();
    return _messages
        .where(
          (message) =>
              message.senderId == userId || message.receiverId == userId,
        )
        .toList();
  }

  /// 获取两个用户之间的对话
  Future<List<Message>> getConversation(String userId1, String userId2) async {
    await getAllMessages();
    return _messages
        .where(
          (message) =>
              (message.senderId == userId1 && message.receiverId == userId2) ||
              (message.senderId == userId2 && message.receiverId == userId1),
        )
        .toList()
      ..sort((a, b) => a.sentAt.compareTo(b.sentAt));
  }

  /// 发送消息
  Future<Message> sendMessage(
    String senderId,
    String receiverId,
    String content,
  ) async {
    final message = Message(
      senderId: senderId,
      receiverId: receiverId,
      content: content,
    );

    _messages.add(message);
    await _saveMessages();
    return message;
  }

  /// 标记消息为已读
  Future<void> markMessageAsRead(String messageId) async {
    final index = _messages.indexWhere((m) => m.id == messageId);
    if (index != -1) {
      _messages[index].isRead = true;
      await _saveMessages();
    }
  }

  /// 从本地存储加载用户数据
  Future<void> _loadUsers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final usersJson = prefs.getString(_usersStorageKey);

      if (usersJson != null) {
        final List<dynamic> decodedList = jsonDecode(usersJson);
        _users = decodedList.map((item) => User.fromJson(item)).toList();
      }
    } catch (e) {
      // 处理错误，初始化为空列表
      _users = [];
    }
  }

  /// 保存用户数据到本地存储
  Future<void> _saveUsers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final usersJson = jsonEncode(
        _users.map((user) => user.toJson()).toList(),
      );
      await prefs.setString(_usersStorageKey, usersJson);
    } catch (e) {
      // 处理保存错误
      debugPrint('保存用户数据失败: $e');
    }
  }

  /// 从本地存储加载当前用户
  Future<void> _loadCurrentUser() async {
    try {
      debugPrint('尝试从本地存储加载用户...');
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(_currentUserKey);

      if (userJson != null) {
        debugPrint('找到本地用户数据: ${userJson.substring(0, userJson.length > 100 ? 100 : userJson.length)}...');
        try {
          _currentUserNotifier.value = User.fromJson(jsonDecode(userJson));
          debugPrint('成功加载本地用户: ${_currentUserNotifier.value?.username}, ID: ${_currentUserNotifier.value?.id}');
        } catch (parseError) {
          debugPrint('解析用户数据失败: $parseError');
          // 如果本地数据损坏，清除并重新尝试
          await prefs.remove(_currentUserKey);
          _currentUserNotifier.value = null;
        }
      } else {
        debugPrint('本地存储中没有用户数据');
        // 尝试从 Supabase 获取
        try {
          await fetchCurrentUser();
        } catch (fetchError) {
          debugPrint('从Supabase获取用户失败: $fetchError');
        }
      }
    } catch (e) {
      debugPrint('加载当前用户失败: $e');
      _currentUserNotifier.value = null;
    }
    
    // 打印最终状态
    debugPrint('用户加载完成. 当前状态: ${_currentUserNotifier.value != null ? "已登录" : "未登录"}');
  }

  /// 保存当前用户到本地存储
  Future<void> _saveCurrentUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (_currentUserNotifier.value != null) {
        debugPrint('正在保存用户到本地存储: ${_currentUserNotifier.value!.username}, ID: ${_currentUserNotifier.value!.id}');
        final json = jsonEncode(_currentUserNotifier.value!.toJson());
        await prefs.setString(_currentUserKey, json);
        debugPrint('用户保存成功, JSON大小: ${json.length}字节');
        
        // 验证存储是否成功
        final savedJson = prefs.getString(_currentUserKey);
        if (savedJson != null) {
          debugPrint('验证: 成功从SharedPreferences读取到用户数据');
        } else {
          debugPrint('错误: 无法从SharedPreferences读取刚保存的用户数据');
        }
      } else {
        debugPrint('清除本地存储的用户信息');
        await prefs.remove(_currentUserKey);
      }
    } catch (e) {
      debugPrint('保存当前用户失败: $e');
    }
  }

  /// 从本地存储加载消息数据
  Future<void> _loadMessages() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final messagesJson = prefs.getString(_messagesStorageKey);

      if (messagesJson != null) {
        final List<dynamic> decodedList = jsonDecode(messagesJson);
        _messages = decodedList.map((item) => Message.fromJson(item)).toList();
      }
    } catch (e) {
      // 处理错误，初始化为空列表
      _messages = [];
    }
  }

  /// 保存消息数据到本地存储
  Future<void> _saveMessages() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final messagesJson = jsonEncode(
        _messages.map((message) => message.toJson()).toList(),
      );
      await prefs.setString(_messagesStorageKey, messagesJson);
    } catch (e) {
      // 处理保存错误
      debugPrint('保存消息数据失败: $e');
    }
  }

  /// 登出
  Future<void> logout() async {
    try {
      debugPrint('开始登出过程');
      
      // 先清除本地用户数据
      debugPrint('清除本地用户数据');
      await clearCurrentUser();
      
      // 然后清除Supabase身份验证
      debugPrint('从Supabase退出');
      await supabase.auth.signOut();
      
      // 验证是否完全退出
      final authUser = supabase.auth.currentUser;
      debugPrint('退出后的身份验证状态: ${authUser == null ? "已退出" : "仍然登录"}');
      
      if (_currentUserNotifier.value != null) {
        debugPrint('警告: 本地用户数据没有完全清除');
        // 再尝试一次清除
        _currentUserNotifier.value = null;
      }
    } catch (e) {
      debugPrint('登出失败: $e');
      throw Exception('登出失败: $e');
    }
  }

  /// 释放资源
  void dispose() {
    _authSubscription?.cancel();
    _currentUserNotifier.dispose();
    debugPrint('UserService资源已释放');
  }
  
  /// 检查邮箱是否已注册
  Future<bool> isEmailRegistered(String email) async {
    try {
      // 尝试查询数据库中是否存在该邮箱
      final response = await supabase
          .from('users')
          .select('id')
          .eq('email', email)
          .maybeSingle();
      
      return response != null;
    } catch (e) {
      debugPrint('检查邮箱是否注册失败: $e');
      return false;
    }
  }
}
