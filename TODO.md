TODO list:

好的，当前代码中的登录逻辑确实存在一些可以优化的地方，主要问题在于：

1.  **状态来源不统一**：同时检查 `supabase.auth.currentUser` 和 `_userService.currentUser` 以及 `_userService.isLoggedIn()`，这使得状态判断变得复杂且容易出错。
2.  **数据获取时机**：在 `_loadUserData` 中，即使 `_userService.currentUser` 存在，也可能与 Supabase 的实际认证状态不一致。如果 Supabase 已登录但 `_userService` 中没有用户（比如应用重启后），尝试从 Supabase 获取是正确的，但整体流程可以更清晰。
3.  **`UserService` 的角色**：`UserService` 应该成为用户状态和用户数据的唯一权威来源 (Single Source of Truth)。它应该内部处理好与 `supabase.auth` 的同步。
4.  **响应式更新**：`ProfilePage` 应该响应式地监听 `UserService` 中的用户状态变化，而不是在 `initState` 中进行一次性加载（尽管 `_loadUserData` 是 `async`，但其触发机制可以改进）。

下面是梳理后的登录逻辑思路（中文）：

**登录逻辑梳理思路：**

1.  **`UserService` 作为核心：**
    *   **监听认证状态变化**：`UserService` 内部应监听 `supabase.auth.onAuthStateChange`. 这是最可靠的知道用户登录或登出事件的方式。
    *   **同步用户数据**：
        *   当 `onAuthStateChange` 事件为 `signedIn` (登录) 时：
            *   `UserService` 根据 `session.user.id` 从你的用户数据表 (例如 Supabase 中的 `profiles` 表) 获取用户详细信息 (昵称、头像等)。
            *   将获取到的 `User` 对象存储在 `UserService` 内部的一个私有变量中 (例如 `_currentUser`)。
            *   通过一个公共的 `Stream<User?>` 或 `ValueNotifier<User?>` (例如 `userStream` 或 `currentUserNotifier`) 暴露当前用户状态。UI 将监听这个流/通知器。
        *   当 `onAuthStateChange` 事件为 `signedOut` (登出) 时：
            *   将 `UserService` 内部的 `_currentUser` 设置为 `null`。
            *   通过 `userStream`/`currentUserNotifier` 发出 `null` 值。
    *   **提供用户访问接口**：
        *   `User? get currentUser`: 返回内部存储的 `_currentUser`。
        *   `Stream<User?> get userStream` (或 `ValueListenable<User?> get currentUserNotifier`): UI 层通过这个来响应式地更新。
        *   `bool get isLoggedIn`: 可以简单地通过 `currentUser != null` 判断。
    *   **登出操作**：
        *   `Future<void> logout()`: 调用 `supabase.auth.signOut()`。`onAuthStateChange` 的监听器会自动处理后续的 `_currentUser` 清理和 `userStream` 的更新。
    *   **初始化**：`UserService` 在其实例化时 (或通过一个 `init()` 方法) 就应该设置 `onAuthStateChange` 监听。这样，应用启动时，如果存在有效的会话，`UserService` 能自动加载用户信息。

2.  **`ProfilePage` 的职责：**
    *   **依赖注入 `UserService`**：通常通过 `Provider`, `Riverpod`, `GetIt` 等方式获取 `UserService` 实例。
    *   **监听用户状态**：
        *   使用 `StreamBuilder` (如果 `UserService` 暴露 `Stream<User?>`) 或 `ValueListenableBuilder` (如果 `UserService` 暴露 `ValueNotifier<User?>`) 来监听用户状态。
        *   `StreamBuilder`/`ValueListenableBuilder` 会根据流/通知器发出的值 (是 `User` 对象还是 `null`) 自动重建 UI，从而显示已登录或未登录视图。
    *   **简化加载逻辑**：`ProfilePage` 不再需要复杂的 `_loadUserData` 方法。其 `build` 方法会直接根据从 `UserService` 获取的当前用户状态来渲染。
    *   **触发操作**：
        *   调用 `_userService.logout()` 来执行登出。
        *   导航到登录页。

**这样做的好处：**

*   **单一数据源**：`UserService` 成为用户状态的唯一权威。
*   **响应式**：UI 自动响应用户状态的变化，无需手动 `setState` 来同步。
*   **代码解耦**：`ProfilePage` 只负责展示和触发操作，`UserService` 负责管理用户状态和与后端交互。
*   **可测试性**：`UserService` 更容易进行单元测试。
*   **健壮性**：减少了因状态不一致导致 bug 的可能性。

---

现在，我们来修改代码。

**第一步：修改 `UserService`**

```dart
// services/user_service.dart
import 'dart:async';
import 'package:flutter/foundation.dart'; // For ValueNotifier
import 'package:supabase_flutter/supabase_flutter.dart' as supabase_auth; // aliasing to avoid conflict
import '../models/user.dart' as app_user; // aliasing your User model
import '../main.dart'; // For supabase client

class UserService {
  // Use ValueNotifier for simpler state management in UI
  // It holds the current user or null if not logged in.
  final ValueNotifier<app_user.User?> _currentUserNotifier = ValueNotifier(null);
  ValueListenable<app_user.User?> get currentUserNotifier => _currentUserNotifier;

  // Getter for direct access, primarily for non-UI logic if needed
  app_user.User? get currentUser => _currentUserNotifier.value;

  // Getter to check login status easily
  bool get isLoggedIn => _currentUserNotifier.value != null;

  StreamSubscription<supabase_auth.AuthState>? _authSubscription;

  UserService() {
    _initialize();
  }

  void _initialize() {
    // Listen to Supabase auth state changes
    _authSubscription = supabase.auth.onAuthStateChange.listen((data) async {
      final supabase_auth.Session? session = data.session;
      debugPrint('Auth event: ${data.event}, Session: ${session != null}');

      if (session != null) {
        // User is logged in or session restored
        if (_currentUserNotifier.value == null || _currentUserNotifier.value!.id != session.user.id) {
          // If local user is not set or different, fetch/update it
          await _fetchAndSetUser(session.user.id);
        }
      } else {
        // User is logged out
        if (_currentUserNotifier.value != null) {
          _currentUserNotifier.value = null;
          debugPrint('User logged out, _currentUserNotifier set to null.');
        }
      }
    });

    // Handle initial state (e.g., app startup, existing session)
    // The onAuthStateChange listener with AuthChangeEvent.initialSession handles this.
    // However, a manual check at startup can be beneficial for immediate UI update.
    final initialSession = supabase.auth.currentSession;
    if (initialSession != null && _currentUserNotifier.value == null) {
        debugPrint('Initial session found, fetching user data.');
        _fetchAndSetUser(initialSession.user.id);
    } else if (initialSession == null && _currentUserNotifier.value != null) {
        _currentUserNotifier.value = null; // Ensure consistency if somehow notifier had a value
    }
  }

  Future<void> _fetchAndSetUser(String userId) async {
    try {
      debugPrint('Fetching user profile for ID: $userId');
      final response = await supabase
          .from('profiles') // Assuming your user profiles table is named 'profiles'
          .select()
          .eq('id', userId)
          .single(); // Use .single() to get one record or throw error

      // Assuming your app_user.User.fromMap constructor exists
      final userProfile = app_user.User.fromMap(response);
      _currentUserNotifier.value = userProfile;
      debugPrint('User profile fetched and set: ${userProfile.username}');

    } catch (e) {
      debugPrint('Error fetching user profile from Supabase: $e');
      // If fetching profile fails after login, we might still have an authenticated user
      // but no profile details. Decide how to handle this.
      // For now, setting to null means login "failed" in terms of app user data.
      // Or, you could create a User object with just the ID and email from session.user.
      _currentUserNotifier.value = null; // Or handle more gracefully
    }
  }

  // Call this if you need to manually refresh user data, e.g., after profile update
  Future<void> refreshCurrentUser() async {
    final supabaseUser = supabase.auth.currentUser;
    if (supabaseUser != null) {
      await _fetchAndSetUser(supabaseUser.id);
    } else {
       debugPrint("Refresh requested but no Supabase user found.");
       if (_currentUserNotifier.value != null) {
         _currentUserNotifier.value = null; // ensure consistency
       }
    }
  }


  Future<void> login(String email, String password) async {
    try {
      final supabase_auth.AuthResponse res = await supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );
      // onAuthStateChange listener will handle fetching user profile
      if (res.user == null) {
        throw Exception('Login failed: No user data received.');
      }
      debugPrint('Supabase login successful for ${res.user!.email}');
    } catch (e) {
      debugPrint('Supabase login error: $e');
      rethrow; // Rethrow to be caught by UI
    }
  }

  Future<void> signup(String email, String password, String username) async {
    try {
      final supabase_auth.AuthResponse res = await supabase.auth.signUp(
        email: email,
        password: password,
        data: {'username': username}, // Add additional data for profile creation if needed
      );
      // onAuthStateChange listener will handle setting the user if auto-confirm is on
      // If email confirmation is required, user won't be set until confirmed.
      if (res.user == null && res.session == null) { // User might be null if email verification is on
          debugPrint('Signup initiated, email verification might be pending.');
      } else if (res.user != null) {
          debugPrint('Supabase signup successful for ${res.user!.email}');
          // You might want to immediately create a profile entry here
          // if your onAuthStateChange doesn't cover new signups fast enough
          // or if you have specific profile creation logic post-signup.
          // For simplicity, we rely on onAuthStateChange for now.
          // Example: await _createProfileEntry(res.user!.id, username, res.user!.email!);
      }
    } catch (e) {
      debugPrint('Supabase signup error: $e');
      rethrow;
    }
  }

  // Example: if you need to create a profile entry immediately after signup
  // and not rely on a trigger or onAuthStateChange for the *initial* profile creation.
  // Future<void> _createProfileEntry(String userId, String username, String email) async {
  //   try {
  //     await supabase.from('profiles').insert({
  //       'id': userId,
  //       'username': username,
  //       'email': email, // Storing email in profiles might be redundant if already in auth.users
  //       'avatar_url': '', // Default avatar
  //     });
  //     debugPrint('Profile entry created for $username');
  //     // After creating, fetch and set to update the notifier
  //     await _fetchAndSetUser(userId);
  //   } catch (e) {
  //     debugPrint('Error creating profile entry: $e');
  //   }
  // }


  Future<void> logout() async {
    try {
      debugPrint('UserService: Attempting to logout from Supabase.');
      await supabase.auth.signOut();
      // onAuthStateChange listener will set _currentUserNotifier.value to null.
      debugPrint('UserService: Supabase signOut completed.');
    } catch (e) {
      debugPrint('UserService: Logout error: $e');
      // Even if signOut fails, good to clear local state as a fallback,
      // though ideally signOut shouldn't fail often.
      if (_currentUserNotifier.value != null) {
          _currentUserNotifier.value = null;
      }
      rethrow;
    }
  }

  // Dispose the subscription when UserService is no longer needed
  // (e.g., if using a DI that disposes singletons)
  void dispose() {
    _authSubscription?.cancel();
    _currentUserNotifier.dispose();
    debugPrint('UserService disposed, auth subscription cancelled.');
  }
}
```

**Assumptions for `UserService`:**
1.  You have a `profiles` table in Supabase.
2.  Your `app_user.User` model has a `fromMap(Map<String, dynamic> map)` factory constructor. Example:
    ```dart
    // models/user.dart
    class User {
      final String id;
      final String username; // Make sure this matches your 'profiles' table column name
      final String email; // Often from auth, but can be in profiles too
      final String avatarUrl;
      final List<String> publishedSpots; // Assuming these are IDs
      final List<String> favoriteSpots;  // Assuming these are IDs

      User({
        required this.id,
        required this.username,
        required this.email,
        this.avatarUrl = '',
        this.publishedSpots = const [],
        this.favoriteSpots = const [],
      });

      factory User.fromMap(Map<String, dynamic> map) {
        return User(
          id: map['id'] ?? '',
          // Supabase often uses 'username' or a custom field for nickname
          // Adjust 'username_column_name' to your actual column name for nickname/display name
          username: map['username'] ?? map['raw_user_meta_data']?['username'] ?? 'No Name',
          // Email might not be in 'profiles' if you only rely on auth.users.
          // If so, you'd get it from supabase.auth.currentUser.email
          email: map['email'] ?? supabase.auth.currentUser?.email ?? '',
          avatarUrl: map['avatar_url'] ?? '',
          // These would typically be fetched from other tables or stored as arrays if your DB supports it
          publishedSpots: List<String>.from(map['published_spots_ids'] ?? []),
          favoriteSpots: List<String>.from(map['favorite_spots_ids'] ?? []),
        );
      }

      // Add a getter for nickname if 'username' is not your display name field
      String get nickname => username;
    }
    ```
    **Important**: Adjust `User.fromMap` to correctly map your Supabase `profiles` table columns to your `User` model fields. Especially `username` and `avatarUrl`. The `publishedSpots` and `favoriteSpots` would likely be foreign keys or array columns in Supabase, so adjust their loading accordingly. For simplicity, I've assumed they are list of IDs in the map for now.

**Second步：修改 `ProfilePage`**

```dart
// profile_page.dart
import 'package:flutter/material.dart';
import '../models/user.dart' as app_user; // aliasing
import '../services/user_service.dart';
// import '../main.dart'; // Supabase client might not be directly needed here anymore

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  // Assuming UserService is a singleton or provided via DI
  // For simplicity, let's instantiate it here. In a real app, use Provider/GetIt.
  final UserService _userService = UserService(); // Or GetIt.I<UserService>();

  // No need for _isLoading and _currentUser here, ValueListenableBuilder handles it.

  @override
  void initState() {
    super.initState();
    // UserService initializes its listener in its constructor.
    // If you need to force a refresh on page load for some reason (usually not necessary):
    // _userService.refreshCurrentUser();
  }

  Future<void> _logout() async {
    try {
      debugPrint('ProfilePage: Initiating logout.');
      await _userService.logout();
      // UI will update automatically via ValueListenableBuilder
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('退出登录成功')),
        );
        // Optionally navigate away, e.g. to home or login page
        // Navigator.of(context).pushNamedAndRemoveUntil('/home', (route) => false);
      }
    } catch (e) {
      debugPrint('ProfilePage: Logout failed: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('退出登录失败: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<app_user.User?>(
      valueListenable: _userService.currentUserNotifier,
      builder: (context, currentUser, child) {
        // The builder is called whenever currentUserNotifier's value changes.
        // No explicit _isLoading needed as UI rebuilds based on currentUser.
        // If _fetchAndSetUser in UserService takes time, you might want a loading state
        // within UserService or show a generic loading while currentUser is null
        // after a login attempt but before profile is fetched.
        // For now, null means "not logged in or loading profile".

        if (currentUser == null) {
          // This also covers the initial state while UserService might be fetching user
          // If you want a distinct "loading" screen, UserService's notifier could emit a special state
          // or you can add a separate isLoading flag in UserService.
          return _buildNotLoggedInView();
        }
        return _buildLoggedInView(currentUser);
      },
    );
  }

  // 构建未登录视图 (No changes needed, but shown for completeness)
  Widget _buildNotLoggedInView() {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.blue),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text('个人中心'),
      ),
      body: SingleChildScrollView(
        child: Column(
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 30),
            decoration: const BoxDecoration(
              color: Colors.blue,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(20),
                bottomRight: Radius.circular(20),
              ),
            ),
            child: Column(
              children: [
                Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    color: Colors.blue.shade300,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.person,
                    size: 60,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildStatItem('0', '漂豆'),
              _buildStatItem('0', '发布'),
              _buildStatItem('0', '收藏'),
            ],
          ),
          const SizedBox(height: 20),
          _buildFunctionButton('关注', Colors.blue.shade100, Icons.people),
          _buildFunctionButton('兑换', Colors.green.shade100, Icons.swap_horiz),
          _buildFunctionButton('设置', Colors.yellow.shade100, Icons.settings),
          const SizedBox(height: 20),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: ElevatedButton(
              onPressed: () => Navigator.pushNamed(context, '/login'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                minimumSize: const Size(double.infinity, 50),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: const Text('登录', style: TextStyle(fontSize: 16)),
            ),
          ),
        ],
      ),
    ),
    );
  }

  // 构建已登录视图 - accepts currentUser as parameter
  Widget _buildLoggedInView(app_user.User currentUser) { // Pass currentUser
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.blue),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text('个人中心'),
      ),
      body: SingleChildScrollView(
        child: Column(
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 30),
            decoration: const BoxDecoration(
              color: Colors.blue,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(20),
                bottomRight: Radius.circular(20),
              ),
            ),
            child: Column(
              children: [
                CircleAvatar(
                  radius: 50,
                  backgroundColor: Colors.blue.shade300,
                  backgroundImage: currentUser.avatarUrl.isNotEmpty
                      ? NetworkImage(currentUser.avatarUrl)
                      : null,
                  child: currentUser.avatarUrl.isEmpty
                      ? Text(
                          currentUser.nickname.isNotEmpty ? currentUser.nickname[0] : "?", // Handle empty nickname
                          style: const TextStyle(fontSize: 40, color: Colors.white),
                        )
                      : null,
                ),
                const SizedBox(height: 10),
                Text(
                  currentUser.nickname,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildStatItem('0', '漂豆'), // Assuming '漂豆' is not in User model yet
              _buildStatItem(
                currentUser.publishedSpots.length.toString(),
                '发布',
              ),
              _buildStatItem(
                currentUser.favoriteSpots.length.toString(),
                '收藏',
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildFunctionButton('兑换', Colors.green.shade100, Icons.swap_horiz),
          _buildFunctionButton('关注', Colors.blue.shade100, Icons.people),
          _buildFunctionButton('设置', Colors.yellow.shade100, Icons.settings),
          const SizedBox(height: 20),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: ElevatedButton(
              onPressed: _logout,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                minimumSize: const Size(double.infinity, 50),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: const Text('退出登录', style: TextStyle(fontSize: 16)),
            ),
          ),
        ],
      ),
    ),
    );
  }

  // _buildStatItem and _buildFunctionButton remain the same
  Widget _buildStatItem(String count, String label) {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            count,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 5),
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFunctionButton(String label, Color color, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Container(
        height: 60,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Icon(Icons.chevron_right), // Icon was missing in your original _buildFunctionButton
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    // If you instantiated UserService here AND it needs disposal, do it.
    // However, if UserService is a singleton managed by GetIt or Provider,
    // its lifecycle is typically managed by the DI container.
    // _userService.dispose(); // Only if _userService has a dispose method and needs it here.
    super.dispose();
  }
}

```

**重要提示和后续步骤：**

1.  **Dependency Injection (DI)**:
    *   在 `ProfilePage` 中直接实例化 `UserService _userService = UserService();` 是为了简单。在真实应用中，你应该使用服务定位器 (如 `GetIt`) 或依赖注入框架 (如 `Provider`, `Riverpod`) 来提供 `UserService` 的单例实例。这样可以确保整个应用共享同一个 `UserService` 实例。
    *   如果使用 DI，`UserService` 的 `dispose` 方法应该由 DI 容器在其生命周期结束时调用。
2.  **`User.fromMap`**: 仔细检查并实现 `User.fromMap` 以正确地从 Supabase `profiles` 表的数据映射到你的 `User` 对象。字段名必须匹配。
3.  **Error Handling in `_fetchAndSetUser`**: 当前，如果获取用户配置失败，`_currentUserNotifier.value` 会被设为 `null`，这会导致用户看起来像是未登录。你可能需要更精细的处理，比如创建一个只包含 ID 和 email (来自 `session.user`) 的 `User` 对象，并标记为 "profile incomplete"。
4.  **Loading State**: `ValueListenableBuilder` 会在 `_currentUserNotifier` 为 `null` 时显示 `_buildNotLoggedInView()`。这在用户实际未登录或用户信息正在加载时都会发生。如果你想区分这两种状态（例如，在加载时显示一个 `CircularProgressIndicator`），`UserService` 中的 `_currentUserNotifier` 可以发出一个更复杂的状态对象，或者 `UserService` 可以暴露一个单独的 `ValueNotifier<bool> isLoadingNotifier`。
    *   一个简单的改进是在 `_fetchAndSetUser` 开始时设置一个 `_isLoadingProfile = true`，结束后设置 `_isLoadingProfile = false`，并在 `ProfilePage` 中检查这个状态（如果 `UserService` 暴露了这个状态）。
5.  **`main.dart` for Supabase init**: Make sure `Supabase.initialize(...)` is called in your `main.dart` before `runApp()`.
6.  **`profiles` Table Security**: 确保你的 `profiles` 表有适当的行级安全 (RLS)策略，例如，用户只能读取自己的个人资料，或者所有已认证用户都可以读取所有人的公开个人资料，但只能修改自己的。

这些修改使登录逻辑更加集中、响应式和易于维护，遵循了Flutter应用开发的常见模式。