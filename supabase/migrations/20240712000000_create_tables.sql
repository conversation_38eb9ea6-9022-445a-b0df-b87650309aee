-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
  id UUID REFERENCES auth.users ON DELETE CASCADE PRIMARY KEY,
  username TEXT UNIQUE NOT NULL,
  nickname TEXT NOT NULL,
  email TEXT UNIQUE NOT NULL,
  bio TEXT,
  avatar_url TEXT,
  published_spots TEXT[] DEFAULT '{}',
  favorite_spots TEXT[] DEFAULT '{}',
  following TEXT[] DEFAULT '{}',
  followers TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建钓点表
CREATE TABLE IF NOT EXISTS fishing_spots (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  latitude DOUBLE PRECISION NOT NULL,
  longitude DOUBLE PRECISION NOT NULL,
  description TEXT,
  likes INTEGER DEFAULT 0,
  unlikes INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建钓点照片表
CREATE TABLE IF NOT EXISTS spot_photos (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  spot_id UUID REFERENCES fishing_spots(id) ON DELETE CASCADE NOT NULL,
  photo_url TEXT NOT NULL,
  is_panorama BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建评论表
CREATE TABLE IF NOT EXISTS comments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  spot_id UUID REFERENCES fishing_spots(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建RLS策略
-- 启用行级安全
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE fishing_spots ENABLE ROW LEVEL SECURITY;
ALTER TABLE spot_photos ENABLE ROW LEVEL SECURITY;
ALTER TABLE comments ENABLE ROW LEVEL SECURITY;

-- 用户表策略
CREATE POLICY "用户可以查看所有用户信息" ON users
  FOR SELECT USING (true);

CREATE POLICY "用户只能更新自己的信息" ON users
  FOR UPDATE USING (auth.uid() = id);

-- 钓点表策略
CREATE POLICY "所有人可以查看钓点" ON fishing_spots
  FOR SELECT USING (true);

CREATE POLICY "已登录用户可以创建钓点" ON fishing_spots
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "用户只能更新自己的钓点" ON fishing_spots
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "用户只能删除自己的钓点" ON fishing_spots
  FOR DELETE USING (auth.uid() = user_id);

-- 钓点照片表策略
CREATE POLICY "所有人可以查看钓点照片" ON spot_photos
  FOR SELECT USING (true);

CREATE POLICY "已登录用户可以上传照片" ON spot_photos
  FOR INSERT WITH CHECK (
    auth.uid() IN (
      SELECT user_id FROM fishing_spots WHERE id = spot_id
    )
  );

CREATE POLICY "用户只能删除自己钓点的照片" ON spot_photos
  FOR DELETE USING (
    auth.uid() IN (
      SELECT user_id FROM fishing_spots WHERE id = spot_id
    )
  );

-- 评论表策略
CREATE POLICY "所有人可以查看评论" ON comments
  FOR SELECT USING (true);

CREATE POLICY "已登录用户可以发表评论" ON comments
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "用户只能删除自己的评论" ON comments
  FOR DELETE USING (auth.uid() = user_id);

-- 创建存储桶
INSERT INTO storage.buckets (id, name, public) VALUES ('spot_photos', '钓点照片', true);

-- 设置存储桶策略
CREATE POLICY "所有人可以查看钓点照片" ON storage.objects
  FOR SELECT USING (bucket_id = 'spot_photos');

CREATE POLICY "已登录用户可以上传照片" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'spot_photos' AND
    auth.uid() IS NOT NULL
  );

CREATE POLICY "用户只能删除自己上传的照片" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'spot_photos' AND
    auth.uid() = owner
  );

-- 创建地理空间索引
CREATE INDEX fishing_spots_location_idx ON fishing_spots USING gist (
  ST_SetSRID(ST_MakePoint(longitude, latitude), 4326)
);

-- 创建函数：根据地图范围获取钓点
CREATE OR REPLACE FUNCTION get_spots_in_bounds(
  min_lat DOUBLE PRECISION,
  min_lng DOUBLE PRECISION,
  max_lat DOUBLE PRECISION,
  max_lng DOUBLE PRECISION,
  limit_count INTEGER DEFAULT 30
)
RETURNS SETOF fishing_spots
LANGUAGE sql
AS $$
  SELECT *
  FROM fishing_spots
  WHERE
    latitude BETWEEN min_lat AND max_lat AND
    longitude BETWEEN min_lng AND max_lng
  ORDER BY created_at DESC
  LIMIT limit_count;
$$;

-- 创建函数：增加点赞数
CREATE OR REPLACE FUNCTION increment_likes(spot_id UUID)
RETURNS void
LANGUAGE sql
AS $$
  UPDATE fishing_spots
  SET likes = likes + 1
  WHERE id = spot_id;
$$;

-- 创建函数：增加不喜欢数
CREATE OR REPLACE FUNCTION increment_unlikes(spot_id UUID)
RETURNS void
LANGUAGE sql
AS $$
  UPDATE fishing_spots
  SET unlikes = unlikes + 1
  WHERE id = spot_id;
$$;

-- 创建函数：数组追加元素
CREATE OR REPLACE FUNCTION array_append(arr TEXT[], val TEXT)
RETURNS TEXT[]
LANGUAGE sql
AS $$
  SELECT array_append(arr, val);
$$;

-- 创建函数：数组移除元素
CREATE OR REPLACE FUNCTION array_remove(arr TEXT[], vals TEXT[])
RETURNS TEXT[]
LANGUAGE sql
AS $$
  SELECT COALESCE(
    (SELECT array_agg(elem) FROM unnest(arr) AS elem WHERE elem <> ALL(vals)),
    '{}'::TEXT[]
  );
$$;
